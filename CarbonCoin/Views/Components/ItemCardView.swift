//
//  ItemCardView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/23.
//

import SwiftUI
import TipKit
import MapKit
import CoreLocation

// MARK: - 删除卡片提示
struct DeleteCardTip: Tip {
    var title: Text {
        Text("删除卡片")
    }

    var message: Text? {
        Text("确定要删除这张卡片吗？此操作无法撤销。")
    }

    var image: Image? {
        Image(systemName: "trash")
    }
}

struct ItemCardView: View {
    let userItemCard: UserItemCard

    // 便利属性，用于访问卡片信息
    private var card: ItemCard {
        userItemCard.card ?? ItemCard(
            id: "placeholder",
            tags: [],
            description: "无法加载卡片信息",
            title: "错误",
            imageFileName: "",
            imageURL: "",
            createdAt: Date(),
            authorId: "",
            location: "",
            latitude: nil,
            longitude: nil
        )
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 图像
            if let image = card.image {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFit()
                    .frame(maxWidth: .infinity, maxHeight: 180)
                    .clipShape(RoundedRectangle(cornerRadius: 10))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    )
            } else {
                Rectangle()
                    .fill(.gray.opacity(0.2))
                    .frame(maxWidth: .infinity, maxHeight: 180)
                    .overlay(
                        Text("Image Unavailable")
                            .foregroundColor(.white)
                            .font(.caption)
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 10))
            }
            
            // 标题
            Text(card.title)
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .padding(.top, 4)
            
            // 标签
            HStack {
                ForEach(card.tags, id: \.self) { tag in
                    Text(tag)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.blue.opacity(0.15))
                        .clipShape(Capsule())
                        .foregroundColor(.blue)
                }
            }
            
            // 描述
            Text(card.description)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineLimit(2)
                .padding(.top, 4)

            // 作者信息
            HStack {
                Image(systemName: "person.circle")
                    .foregroundColor(.secondary)
                Text("作者: \(userItemCard.author?.nickname ?? "未知用户")")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
            }
            .padding(.top, 2)
        }
        .padding()
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .gray.opacity(0.2), radius: 4, x: 0, y: 2)
        .padding(.horizontal, 8)
        .onTapGesture {
            print("Tapped card: \(card.title) (ID: \(card.id))")
        }
    }
}

// MARK: - 卡片缩略图视图
struct ItemCardThumbnailView: View {
    let card: ItemCard
    @EnvironmentObject var cardStore: CardStore

    var body: some View {
        NavigationLink(destination: ItemCardDetailView(
            userItemCard: UserItemCard(
                id: UUID().uuidString,
                userId: "",
                cardId: card.id,
                remark: nil,
                acquiredAt: Date(),
                isOwner: false,
                card: card,
                author: nil
            ),
            cardViewModel: ItemCardViewModel(itemCard: card),
            cardStore: cardStore
        )) {
            cardContent
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var cardContent: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
            cardImage
            cardTitle
            cardTags
            cardDate
        }
        .padding(Theme.Spacing.sm)
        .background(Color.cardBackground.opacity(0.1))
        .cornerRadius(Theme.CornerRadius.lg)
        .glassCard()
    }

    private var cardImage: some View {
        Group {
            if let image = card.image {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFill()
                    .frame(height: 120)
                    .clipped()
                    .cornerRadius(Theme.CornerRadius.md)
            } else {
                Rectangle()
                    .fill(Color.cardBackground.opacity(0.3))
                    .frame(height: 120)
                    .cornerRadius(Theme.CornerRadius.md)
                    .overlay(
                        Image(systemName: "photo")
                            .font(.title2)
                            .foregroundColor(.textSecondary)
                    )
            }
        }
    }

    private var cardTitle: some View {
        Text(card.title)
            .font(.bodyBrand)
            .foregroundColor(.textPrimary)
            .lineLimit(2)
            .multilineTextAlignment(.leading)
    }

    private var cardTags: some View {
        HStack {
            ForEach(Array(card.tags.prefix(2)), id: \.self) { tag in
                Text(tag)
                    .font(.caption2)
                    .foregroundColor(.textPrimary)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.brandGreen.opacity(0.2))
                    .cornerRadius(Theme.CornerRadius.sm)
            }

            if card.tags.count > 2 {
                Text("+\(card.tags.count - 2)")
                    .font(.caption2)
                    .foregroundColor(.textSecondary)
            }

            Spacer()
        }
    }

    private var cardDate: some View {
        Text(card.formattedCreatedAt)
            .font(.caption2)
            .foregroundColor(.textSecondary)
    }
}

// MARK: - 卡片详情视图
struct ItemCardDetailView: View {
    let userItemCard: UserItemCard
    @StateObject var cardViewModel: ItemCardViewModel
    let cardStore: CardStore
    @StateObject private var userItemCardViewModel = UserItemCardViewModel()
    @Environment(\.dismiss) private var dismiss
    @State private var isCropping = false
    @State private var showDeleteConfirmation = false
    @State private var isSharing = false
    @State private var shareURL: String?
    private let deleteCardTip = DeleteCardTip()

    // 便利属性，用于访问卡片信息
    private var card: ItemCard {
        userItemCard.card ?? cardViewModel.itemCard
    }

    var body: some View {
        NavigationStack {
            ZStack {
                CustomAngularGradient()

                ScrollView {
                    VStack(alignment: .leading, spacing: Theme.Spacing.lg) {
                        // 图像
                        if let image = card.image {
                            Image(uiImage: image)
                                .resizable()
                                .scaledToFit()
                                .frame(maxHeight: 300)
                                .cornerRadius(Theme.CornerRadius.lg)
                                .croppable($isCropping, image: Binding(
                                    get: { card.image },
                                    set: { newImage in
                                        guard let newImage = newImage else { return }
                                        cardViewModel.updateImage(newImage)
                                        // 同步更新到 CardStore
                                        cardStore.updateCard(cardViewModel.itemCard)
                                    }
                                ))
                                .glassCard()
                        }

                        // 标题和时间
                        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                            Text(card.title)
                                .font(.title2Brand)
                                .foregroundColor(.textPrimary)

                            Text("创建于 \(card.formattedCreatedAt)")
                                .font(.captionBrand)
                                .foregroundColor(.textSecondary)
                        }
                        .padding(Theme.Spacing.md)
                        .glassCard()

                        // 作者信息
                        if let author = userItemCard.author {
                            VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                                Text("作者")
                                    .font(.title3Brand)
                                    .foregroundColor(.textPrimary)

                                HStack {
                                    Image(systemName: "person.circle.fill")
                                        .foregroundColor(.brandGreen)
                                    Text(author.nickname)
                                        .font(.bodyBrand)
                                        .foregroundColor(.textSecondary)
                                }
                            }
                            .padding(Theme.Spacing.md)
                            .glassCard()
                        }

                        // 描述
                        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                            Text("描述")
                                .font(.title3Brand)
                                .foregroundColor(.textPrimary)

                            Text(card.description)
                                .font(.bodyBrand)
                                .foregroundColor(.textSecondary)
                        }
                        .padding(Theme.Spacing.md)
                        .glassCard()

                        // 标签
                        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                            Text("标签")
                                .font(.title3Brand)
                                .foregroundColor(.textPrimary)

                            LazyVGrid(columns: [
                                GridItem(.adaptive(minimum: 80), spacing: 8)
                            ], spacing: 8) {
                                ForEach(card.tags, id: \.self) { tag in
                                    Text(tag)
                                        .font(.captionBrand)
                                        .foregroundColor(.textPrimary)
                                        .padding(.horizontal, Theme.Spacing.sm)
                                        .padding(.vertical, 4)
                                        .background(Color.brandGreen.opacity(0.2))
                                        .cornerRadius(Theme.CornerRadius.sm)
                                }
                            }
                        }
                        .padding(Theme.Spacing.md)
                        .glassCard()

                        // 位置信息（可点击查看地图）
                        if !card.location.isEmpty {
                            LocationInfoView(
                                location: card.location,
                                latitude: card.latitude,
                                longitude: card.longitude
                            )
                        }

                        // 备注信息（可编辑）
                        RemarkEditView(
                            userItemCard: userItemCard,
                            userItemCardViewModel: userItemCardViewModel
                        )

                        Spacer(minLength: Theme.Spacing.tab)
                    }
                    .padding(.horizontal, Theme.Spacing.md)
                }
                .scrollContentBackground(.hidden)
            }
            .navigationTitle("卡片详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button {
                        showDeleteConfirmation = true
                    } label: {
                        Image(systemName: "trash")
                            .foregroundColor(.red)
                    }
                    .popoverTip(deleteCardTip)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("共享卡片") {
                            shareCard()
                        }
                        Button("完成") {
                            dismiss()
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                            .foregroundColor(.brandGreen)
                    }
                }
            }
            .confirmationDialog("删除卡片", isPresented: $showDeleteConfirmation) {
                Button("删除", role: .destructive) {
                    // 删除卡片
                    cardStore.deleteCard(cardViewModel.itemCard)
                    dismiss()
                }
                Button("取消", role: .cancel) { }
            } message: {
                Text("确定要删除这张卡片吗？此操作无法撤销。")
            }
            .sheet(isPresented: $isSharing) {
                if let shareURL = shareURL {
                    ShareSheet(activityItems: [shareURL])
                }
            }
        }
    }

    // MARK: - 共享功能
    private func shareCard() {
        Task {
            await handleCardShare()
        }
    }

    @MainActor
    private func handleCardShare() async {
        // 检查是否已有imageURL
        if !cardViewModel.itemCard.imageURL.isEmpty {
            // 直接使用现有URL进行共享
            shareURL = cardViewModel.itemCard.imageURL
            isSharing = true
            return
        }

        // 如果没有imageURL，需要先上传图片
        guard let image = cardViewModel.itemCard.image,
              let imageData = image.pngData() else {
            print("无法获取图片数据")
            return
        }

        do {
            // 上传图片获取URL
            let uploadedURL = try await ImageShareService.shared.uploadImage(imageData)

            // 更新卡片的imageURL
            cardViewModel.updateImageURL(uploadedURL)
            cardStore.updateCard(cardViewModel.itemCard)

            // 进行共享
            shareURL = uploadedURL
            isSharing = true

        } catch {
            print("图片上传失败，无法共享: \(error.localizedDescription)")
            // 可以在这里显示错误提示
        }
    }
}

// MARK: - 备注编辑视图
struct RemarkEditView: View {
    let userItemCard: UserItemCard
    @ObservedObject var userItemCardViewModel: UserItemCardViewModel
    @State private var isEditing = false
    @State private var editingRemark = ""
    @State private var isSaving = false
    @AppStorage("currentUserId") private var currentUserId: String = ""

    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
            HStack {
                Text("备注")
                    .font(.title3Brand)
                    .foregroundColor(.textPrimary)

                Spacer()

                if isSaving {
                    ProgressView()
                        .scaleEffect(0.8)
                } else {
                    Button(isEditing ? "保存" : "编辑") {
                        if isEditing {
                            // 保存备注到服务器
                            Task {
                                await saveRemark()
                            }
                        } else {
                            // 开始编辑
                            editingRemark = userItemCard.remark ?? ""
                            isEditing = true
                        }
                    }
                    .font(.captionBrand)
                    .foregroundColor(.brandGreen)
                    .disabled(isSaving)
                }
            }

            if isEditing {
                TextField("添加备注...", text: $editingRemark, axis: .vertical)
                    .TextFieldBG()
                    .lineLimit(3...6)
                    .disabled(isSaving)
            } else {
                if let remark = userItemCard.remark, !remark.isEmpty {
                    Text(remark)
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                } else {
                    Text("暂无备注")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary.opacity(0.6))
                        .italic()
                }
            }

            // 显示错误信息
            if let errorMessage = userItemCardViewModel.errorMessage {
                Text(errorMessage)
                    .font(.captionBrand)
                    .foregroundColor(.red)
            }
        }
        .padding(Theme.Spacing.md)
        .glassCard()
    }

    // MARK: - 保存备注方法
    @MainActor
    private func saveRemark() async {
        guard !currentUserId.isEmpty else {
            print("❌ 用户未登录，无法保存备注")
            return
        }

        isSaving = true

        do {
            // 调用UserItemCardViewModel保存备注到服务器
            try await userItemCardViewModel.itemCardManager.updateUserItemCardRemark(
                userId: currentUserId,
                cardId: userItemCard.cardId,
                remark: editingRemark
            )

            print("✅ 备注保存成功: \(editingRemark)")
            isEditing = false

            // 清除错误信息
            userItemCardViewModel.errorMessage = nil

        } catch {
            print("❌ 备注保存失败: \(error.localizedDescription)")
            userItemCardViewModel.errorMessage = "保存失败: \(error.localizedDescription)"
        }

        isSaving = false
    }
}

// MARK: - 系统共享组件
struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: activityItems, applicationActivities: nil)
        return controller
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - 位置信息视图
struct LocationInfoView: View {
    let location: String
    let latitude: Double?
    let longitude: Double?
    @State private var showingMapSheet = false

    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
            Text("位置")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            Button(action: {
                if latitude != nil && longitude != nil {
                    showingMapSheet = true
                }
            }) {
                HStack {
                    Image(systemName: "mappin.and.ellipse")
                        .foregroundColor(.brandGreen)

                    Text(location)
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)

                    Spacer()

                    if latitude != nil && longitude != nil {
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.textSecondary.opacity(0.6))
                    }
                }
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(Theme.Spacing.md)
        .glassCard()
        .sheet(isPresented: $showingMapSheet) {
            if let lat = latitude, let lon = longitude {
                LocationMapSheet(
                    coordinate: CLLocationCoordinate2D(latitude: lat, longitude: lon),
                    locationName: location
                )
            }
        }
    }
}

// MARK: - 地图显示Sheet
struct LocationMapSheet: View {
    let coordinate: CLLocationCoordinate2D
    let locationName: String
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationStack {
            ZStack {
                CustomAngularGradient()

                VStack(spacing: Theme.Spacing.lg) {
                    // 地图视图
                    Map(initialPosition: .region(
                        MKCoordinateRegion(
                            center: coordinate,
                            span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
                        )
                    )) {
                        Marker(locationName, coordinate: coordinate)
                            .tint(.brand)
                    }
                    .frame(height: 400)
                    .cornerRadius(Theme.CornerRadius.lg)
                    .glassCard()

                    // 位置信息
                    VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                        Text("位置信息")
                            .font(.title3Brand)
                            .foregroundColor(.textPrimary)

                        Text(locationName)
                            .font(.bodyBrand)
                            .foregroundColor(.textSecondary)

                        Text("纬度: \(coordinate.latitude, specifier: "%.6f")")
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary.opacity(0.8))

                        Text("经度: \(coordinate.longitude, specifier: "%.6f")")
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary.opacity(0.8))
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(Theme.Spacing.md)
                    .glassCard()

                    Spacer()
                }
                .padding(Theme.Spacing.md)
            }
            .navigationTitle("位置详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                    .foregroundColor(.brandGreen)
                }
            }
        }
    }
}

struct ItemCardView_Previews: PreviewProvider {
    static let sampleCard = ItemCard(
        id: "sample-card-id",
        tags: ["塑料瓶身", "纸质杯托"],
        description: "这是一个赛百味品牌的杯子。",
        title: "赛百味杯子",
        imageFileName: "",
        imageURL: "",
        createdAt: Date(),
        authorId: "sample_user",
        location: "北京市朝阳区",
        latitude: 39.9042,
        longitude: 116.4074
    )

    static let sampleUserItemCard = UserItemCard(
        id: "sample-user-item-card-id",
        userId: "sample_user",
        cardId: "sample-card-id",
        remark: "这是我的备注",
        acquiredAt: Date(),
        isOwner: true,
        card: sampleCard,
        author: AuthorInfo(userId: "sample_user", nickname: "测试用户", avatarURL: nil)
    )

    static var previews: some View {
        Group {
            // 原始卡片视图
            ItemCardView(userItemCard: sampleUserItemCard)
                .previewLayout(.sizeThatFits)
                .padding()
                .background(Color.gray.opacity(0.1))
                .previewDisplayName("Original Card View")

            // 缩略图视图
            ItemCardThumbnailView(card: sampleCard)
                .environmentObject(CardStore())
                .previewLayout(.sizeThatFits)
                .padding()
                .background(Color.gray.opacity(0.1))
                .previewDisplayName("Thumbnail View")

            // 详情视图
            ItemCardDetailView(
                userItemCard: sampleUserItemCard,
                cardViewModel: ItemCardViewModel(itemCard: sampleCard),
                cardStore: CardStore()
            )
            .previewDisplayName("Detail View")
        }
    }
}
