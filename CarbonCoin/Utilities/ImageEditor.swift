//
//  ImageEditor.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/24.
//

import SwiftUI
import CropViewController  // Swift 封装的 TOCropViewController

// MARK: 图片裁切工具
struct ImageEditor {
    /// 返回一个可以裁剪图片的 SwiftUI View
    static func cropper(image: UIImage, completion: @escaping (UIImage?) -> Void) -> some View {
        CropperSheet(image: image, onComplete: completion)
    }

    private struct CropperSheet: UIViewControllerRepresentable {
        let image: UIImage
        let onComplete: (UIImage?) -> Void
        @Environment(\.presentationMode) var presentationMode

        func makeCoordinator() -> Coordinator {
            Coordinator(self)
        }

        func makeUIViewController(context: Context) -> CropViewController {
            let cropVC = CropViewController(image: image)
            cropVC.delegate = context.coordinator
            cropVC.aspectRatioPickerButtonHidden = true
            cropVC.rotateButtonsHidden = false
            cropVC.aspectRatioLockEnabled = false
            cropVC.resetButtonHidden = false
            cropVC.cropView.cropBoxResizeEnabled = true
            return cropVC
        }

        func updateUIViewController(_ uiViewController: CropViewController, context: Context) {}

        class Coordinator: NSObject, CropViewControllerDelegate {
            let parent: CropperSheet
            init(_ parent: CropperSheet) { self.parent = parent }

            func cropViewController(_ cropViewController: CropViewController,
                                    didCropToImage image: UIImage,
                                    withRect: CGRect,
                                    angle: Int) {
                parent.onComplete(image)
                parent.presentationMode.wrappedValue.dismiss()
            }

            func cropViewController(_ cropViewController: CropViewController,
                                    didFinishCancelled cancelled: Bool) {
                parent.onComplete(nil)
                parent.presentationMode.wrappedValue.dismiss()
            }
        }
    }
}


// MARK: 图片选择器
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.presentationMode) var presentationMode
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
            }
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}

